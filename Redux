-- Redux UI System - Roblox Studio 2025
-- UI básica com componentes modernos

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- <PERSON>riar ScreenGui principal
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "ReduxUI"
screenGui.Parent = playerGui
screenGui.ResetOnSpawn = false
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

-- Frame principal
local mainFrame = Instance.new("Frame")
mainFrame.Name = "MainFrame"
mainFrame.Parent = screenGui
mainFrame.Size = UDim2.new(0, 400, 0, 300)
mainFrame.Position = UDim2.new(0.5, -200, 0.5, -150)
mainFrame.BackgroundColor3 = Color3.fromRGB(45, 45, 55)
mainFrame.BorderSizePixel = 0

-- UICorner para o frame principal
local mainCorner = Instance.new("UICorner")
mainCorner.Parent = mainFrame
mainCorner.CornerRadius = UDim.new(0, 12)

-- UIPadding para o frame principal
local mainPadding = Instance.new("UIPadding")
mainPadding.Parent = mainFrame
mainPadding.PaddingTop = UDim.new(0, 20)
mainPadding.PaddingBottom = UDim.new(0, 20)
mainPadding.PaddingLeft = UDim.new(0, 20)
mainPadding.PaddingRight = UDim.new(0, 20)

-- UIStroke para borda moderna
local mainStroke = Instance.new("UIStroke")
mainStroke.Parent = mainFrame
mainStroke.Color = Color3.fromRGB(80, 80, 90)
mainStroke.Thickness = 2
mainStroke.Transparency = 0.3

-- Título
local titleLabel = Instance.new("TextLabel")
titleLabel.Name = "TitleLabel"
titleLabel.Parent = mainFrame
titleLabel.Size = UDim2.new(1, 0, 0, 40)
titleLabel.Position = UDim2.new(0, 0, 0, 0)
titleLabel.BackgroundTransparency = 1
titleLabel.Text = "Redux UI System"
titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
titleLabel.TextScaled = true
titleLabel.Font = Enum.Font.GothamBold

-- Container para botões
local buttonContainer = Instance.new("Frame")
buttonContainer.Name = "ButtonContainer"
buttonContainer.Parent = mainFrame
buttonContainer.Size = UDim2.new(1, 0, 1, -60)
buttonContainer.Position = UDim2.new(0, 0, 0, 60)
buttonContainer.BackgroundTransparency = 1

-- UIListLayout para organizar botões
local listLayout = Instance.new("UIListLayout")
listLayout.Parent = buttonContainer
listLayout.SortOrder = Enum.SortOrder.LayoutOrder
listLayout.Padding = UDim.new(0, 10)
listLayout.FillDirection = Enum.FillDirection.Vertical

-- Função para criar botões
local function createButton(name, text, layoutOrder)
    local button = Instance.new("TextButton")
    button.Name = name
    button.Parent = buttonContainer
    button.Size = UDim2.new(1, 0, 0, 45)
    button.BackgroundColor3 = Color3.fromRGB(60, 60, 70)
    button.BorderSizePixel = 0
    button.Text = text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.Gotham
    button.LayoutOrder = layoutOrder

    -- UICorner para botão
    local buttonCorner = Instance.new("UICorner")
    buttonCorner.Parent = button
    buttonCorner.CornerRadius = UDim.new(0, 8)

    -- UIPadding para botão
    local buttonPadding = Instance.new("UIPadding")
    buttonPadding.Parent = button
    buttonPadding.PaddingTop = UDim.new(0, 8)
    buttonPadding.PaddingBottom = UDim.new(0, 8)
    buttonPadding.PaddingLeft = UDim.new(0, 15)
    buttonPadding.PaddingRight = UDim.new(0, 15)

    -- UIStroke para botão
    local buttonStroke = Instance.new("UIStroke")
    buttonStroke.Parent = button
    buttonStroke.Color = Color3.fromRGB(100, 100, 110)
    buttonStroke.Thickness = 1
    buttonStroke.Transparency = 0.5

    -- Animações de hover
    local hoverTween = TweenService:Create(
        button,
        TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundColor3 = Color3.fromRGB(80, 80, 90)}
    )

    local normalTween = TweenService:Create(
        button,
        TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundColor3 = Color3.fromRGB(60, 60, 70)}
    )

    button.MouseEnter:Connect(function()
        hoverTween:Play()
    end)

    button.MouseLeave:Connect(function()
        normalTween:Play()
    end)

    return button
end

-- Criar botões
local button1 = createButton("Button1", "Opção 1", 1)
local button2 = createButton("Button2", "Opção 2", 2)
local button3 = createButton("Button3", "Opção 3", 3)
local closeButton = createButton("CloseButton", "Fechar", 4)

-- Funcionalidades dos botões
button1.MouseButton1Click:Connect(function()
    print("Botão 1 clicado!")
end)

button2.MouseButton1Click:Connect(function()
    print("Botão 2 clicado!")
end)

button3.MouseButton1Click:Connect(function()
    print("Botão 3 clicado!")
end)

closeButton.MouseButton1Click:Connect(function()
    screenGui:Destroy()
end)

-- Tornar o frame arrastável
local dragging = false
local dragStart = nil
local startPos = nil

mainFrame.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = true
        dragStart = input.Position
        startPos = mainFrame.Position
    end
end)

mainFrame.InputChanged:Connect(function(input)
    if dragging and input.UserInputType == Enum.UserInputType.MouseMovement then
        local delta = input.Position - dragStart
        mainFrame.Position = UDim2.new(
            startPos.X.Scale,
            startPos.X.Offset + delta.X,
            startPos.Y.Scale,
            startPos.Y.Offset + delta.Y
        )
    end
end)

UserInputService.InputEnded:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 then
        dragging = false
    end
end)

print("Redux UI carregada com sucesso!")